'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { db } from '../lib/firebase';
import { useAuth } from '../hooks/useAuth';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('system');
  const [resolvedTheme, setResolvedTheme] = useState('light');
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  // System theme detection
  const getSystemTheme = () => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  };

  // Load theme from localStorage or Firebase
  useEffect(() => {
    const loadTheme = async () => {
      try {
        // First, try to load from localStorage for immediate application
        const localTheme = localStorage.getItem('theme');
        if (localTheme && ['light', 'dark', 'system'].includes(localTheme)) {
          setTheme(localTheme);
        }

        // If user is authenticated, sync with Firebase
        if (user) {
          try {
            const userDoc = await db.collection('users').doc(user.uid).get();
            if (userDoc.exists && userDoc.data().theme) {
              const firebaseTheme = userDoc.data().theme;
              setTheme(firebaseTheme);
              localStorage.setItem('theme', firebaseTheme);
            }
          } catch (error) {
            console.warn('Failed to load theme from Firebase:', error);
          }
        }
      } catch (error) {
        console.warn('Failed to load theme:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTheme();
  }, [user]);

  // Update resolved theme when theme or system preference changes
  useEffect(() => {
    const updateResolvedTheme = () => {
      if (theme === 'system') {
        setResolvedTheme(getSystemTheme());
      } else {
        setResolvedTheme(theme);
      }
    };

    updateResolvedTheme();

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = () => {
      if (theme === 'system') {
        setResolvedTheme(getSystemTheme());
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  // Apply theme to document
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const root = window.document.documentElement;
      root.classList.remove('light', 'dark');
      root.classList.add(resolvedTheme);
      
      // Update meta theme-color for mobile browsers
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute('content', resolvedTheme === 'dark' ? '#1f2937' : '#ffffff');
      }
    }
  }, [resolvedTheme]);

  // Change theme function
  const changeTheme = async (newTheme) => {
    if (!['light', 'dark', 'system'].includes(newTheme)) return;

    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);

    // Save to Firebase if user is authenticated
    if (user) {
      try {
        await db.collection('users').doc(user.uid).set(
          { theme: newTheme, updatedAt: new Date() },
          { merge: true }
        );
      } catch (error) {
        console.warn('Failed to save theme to Firebase:', error);
      }
    }
  };

  // Toggle between light and dark (skip system)
  const toggleTheme = () => {
    const newTheme = resolvedTheme === 'dark' ? 'light' : 'dark';
    changeTheme(newTheme);
  };

  const value = {
    theme,
    resolvedTheme,
    isLoading,
    changeTheme,
    toggleTheme,
    isDark: resolvedTheme === 'dark',
    isLight: resolvedTheme === 'light',
    isSystem: theme === 'system'
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
