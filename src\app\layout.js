import '../styles/globals.css'
import Header from '../components/Header'
import Footer from '../components/Footer'
import ChatbotProvider from '../components/ChatbotProvider'
import ServiceWorkerRegistration from '../components/ServiceWorkerRegistration'
import PerformanceMonitor from '../components/PerformanceMonitor'
import WhatsAppFloatingButton from '../components/WhatsAppFloatingButton'
import PWAInstallPrompt from '../components/PWAInstallPrompt'
import TouchNavigation from '../components/mobile/TouchNavigation'
import { AuthProvider } from '../contexts/AuthContext'
import { ThemeProvider } from '../contexts/ThemeContext'

export const metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),
  title: 'Top Engineering Colleges in Bangalore | College Comparison & Rankings',
  description: 'Compare top engineering colleges in Bangalore. Get detailed information about placements, courses, rankings, and campus facilities. Find your perfect engineering college match.',
  keywords: 'engineering colleges bangalore, college comparison, placement statistics, RVCE, MSRIT, PES University, BMS College, college rankings',
  authors: [{ name: 'College Comparison Platform' }],
  openGraph: {
    title: 'Top Engineering Colleges in Bangalore | College Comparison',
    description: 'Compare top engineering colleges in Bangalore with detailed placement statistics, course information, and campus facilities.',
    type: 'website',
    locale: 'en_IN',
    siteName: 'Bangalore Engineering Colleges',
  },
  twitter: {
    card: 'summary',
    title: 'Top Engineering Colleges in Bangalore',
    description: 'Compare engineering colleges in Bangalore with detailed insights.',
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <meta name="theme-color" content="#2563eb" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="BEC Compare" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#2563eb" />
        <meta name="msapplication-tap-highlight" content="no" />

        {/* Apple Touch Icons */}
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />
        <link rel="apple-touch-icon" sizes="167x167" href="/icons/icon-192x192.png" />

        {/* Splash Screens for iOS */}
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png" />

        {/* Microsoft Tiles */}
        <meta name="msapplication-TileImage" content="/icons/icon-144x144.png" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        
        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebSite",
              "name": "Bangalore Engineering Colleges Comparison",
              "description": "Compare top engineering colleges in Bangalore with detailed placement statistics and course information",
              "url": "https://bangalore-engineering-colleges.com",
              "potentialAction": {
                "@type": "SearchAction",
                "target": "https://bangalore-engineering-colleges.com/colleges?search={search_term_string}",
                "query-input": "required name=search_term_string"
              }
            })
          }}
        />
      </head>
      <body className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col transition-colors duration-200">
        <ThemeProvider>
          <AuthProvider>
            <Header />
            <main className="flex-grow">
              {children}
            </main>
            <Footer />

        {/* Performance Monitoring */}
        <PerformanceMonitor />

        {/* Service Worker Registration */}
        <ServiceWorkerRegistration />

        {/* Chatbot Integration */}
        <ChatbotProvider />

          {/* Enhanced WhatsApp Floating Button */}
          <WhatsAppFloatingButton />

          {/* PWA Install Prompt */}
          <PWAInstallPrompt />

          {/* Mobile Touch Navigation */}
          <TouchNavigation />
        </AuthProvider>
      </body>
    </html>
  )
}
